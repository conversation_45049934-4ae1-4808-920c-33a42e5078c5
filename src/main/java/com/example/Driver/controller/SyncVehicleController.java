package com.example.Driver.controller;

import com.example.Driver.model.Vehicle;
import org.springframework.web.bind.annotation.*;
import org.springframework.http.ResponseEntity;
import org.springframework.core.io.ClassPathResource;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.web.client.RestTemplate;

import java.io.InputStream;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.HashMap;
import java.util.Map;
import java.util.List;
import java.util.ArrayList;
import java.sql.*;
import org.springframework.web.client.RestTemplate;
import com.example.Driver.CommonUtils;
import com.fasterxml.jackson.databind.ObjectMapper;

@RestController
@RequestMapping("/api")
public class SyncVehicleController {

    // Database connection constants (same as SyncSpecification)
    static final String JDBC_URL =
        "**************************************************************************************************";
    static final String USERNAME = "lumi_digital";
    static final String PASSWORD = "Lu3!_D@2022";

  @GetMapping("/sync-vehicles")
  public ResponseEntity<Map<String, Object>> syncVehicles() {
    Map<String, Object> response = new HashMap<>();
    List<Vehicle> vehicles = new ArrayList<>();

    try {
      // Read Excel file from resources
      ClassPathResource resource = new ClassPathResource("All_Vehicle_Report.xlsx");
      InputStream inputStream = resource.getInputStream();

      Workbook workbook = new XSSFWorkbook(inputStream);
      Sheet sheet = workbook.getSheetAt(0); // Read first sheet

      System.out.println("Reading All_Vehicle_Report.xlsx...");
      System.out.println("Total rows: " + sheet.getLastRowNum());

      // Skip header row, start from row 1 (0-indexed)
      for (int rowIndex = 1; rowIndex <= sheet.getLastRowNum() ; rowIndex++) {
        Row row = sheet.getRow(rowIndex);
        if (row != null) {
          try {
            // Extract data from specific columns
            String assetId = getCellValueAsString(row.getCell(0)); // Column 1: asset_id
            String plateNo = getCellValueAsString(row.getCell(1)); // Column 2: plate_no
            // check if plate already exists in database
            String make = getCellValueAsString(row.getCell(5)); // Column 6: make
            String modelName = getCellValueAsString(row.getCell(6)); // Column 7: model_name
            long modelYear = (long) row.getCell(7).getNumericCellValue(); // Column 8: model_year
            String color = getCellValueAsString(row.getCell(8)); // Column 9: color
            String purchaseDateStr = getCellValueAsString(row.getCell(20)); // Column 21: purchase_date
            String soldDateStr = getCellValueAsString(row.getCell(22)); // Column 22: sold_date
            LocalDate purchaseDate = parseDate(purchaseDateStr);
            LocalDate soldDate = parseDate(soldDateStr);

            // If sold_date is empty, set to '2001-01-01'
            if (soldDate == null || soldDateStr.trim().isEmpty()) {
              soldDate = LocalDate.of(2001, 1, 1);
            }

            // Check if plate already exists in database
            if (checkIfPlateExists(plateNo)) {
              System.out.println("Plate number " + plateNo + " already exists, skipping row " + (rowIndex + 1));
              continue;
            }

            // Get CODEX_ID from database using modelName
            String codexId = getCodexIdFromDatabase(modelName);

            // Get PURCHASE_PRICE from database using assetId
            String purchasePrice = getPurchasePriceFromDatabase(assetId);

            // Create Vehicle object
            Vehicle vehicle =
                new Vehicle(assetId, plateNo, make, modelName, modelYear, color, purchasePrice, purchaseDate, soldDate, codexId);

            vehicles.add(vehicle);

            // Push vehicle data to Kafka
            pushVehicleToKafka(vehicle);

            // Print vehicle details
            System.out.println("Row " + (rowIndex + 1) + ": " + vehicle);

          } catch (Exception e) {
            System.out.println("Error processing row " + (rowIndex + 1) + ": " + e.getMessage());
          }
        }
      }

      workbook.close();
      inputStream.close();

      // Print summary
      System.out.println("\n=== SYNC VEHICLES SUMMARY ===");
      System.out.println("Total vehicles processed: " + vehicles.size());
      System.out.println("\nAll Vehicles:");

      response.put("success", true);
      response.put("totalVehiclesProcessed", vehicles.size());
      response.put("message", "Vehicles synced successfully from All_Vehicle_Report.xlsx");

    } catch (Exception e) {
      response.put("success", false);
      response.put("error", "Error reading Excel file: " + e.getMessage());
      e.printStackTrace();
    }

    return ResponseEntity.ok(response);
  }

  private String getCellValueAsString(Cell cell) {
    if (cell == null) {
      return "";
    }

    switch (cell.getCellType()) {
      case STRING:
        return cell.getStringCellValue().trim();
      case NUMERIC:
        if (DateUtil.isValidExcelDate(cell.getNumericCellValue())) {
          // For date cells, format as date string
          return DateTimeFormatter.ofPattern("dd-MM-yyyy")
              .format(cell.getLocalDateTimeCellValue().toLocalDate());
        } else {
          // For numeric cells, return as string without decimal
          double numericValue = cell.getNumericCellValue();
          if (numericValue == (long) numericValue) {
            return String.valueOf((long) numericValue);
          } else {
            return String.valueOf(numericValue);
          }
        }
      case BOOLEAN:
        return String.valueOf(cell.getBooleanCellValue());
      case FORMULA:
        return cell.getCellFormula();
      default:
        return "";
    }
  }

  private LocalDate parseDate(String dateStr) {
    if (dateStr == null || dateStr.trim().isEmpty()) {
      return null;
    }

    try {
      // Try different date formats
      DateTimeFormatter[] formatters = {
        DateTimeFormatter.ofPattern("yyyy-MM-dd"),
        DateTimeFormatter.ofPattern("dd/MM/yyyy"),
        DateTimeFormatter.ofPattern("MM/dd/yyyy"),
        DateTimeFormatter.ofPattern("dd-MM-yyyy"),
        DateTimeFormatter.ofPattern("yyyy/MM/dd")
      };

      for (DateTimeFormatter formatter : formatters) {
        try {
          return LocalDate.parse(dateStr.trim(), formatter);
        } catch (DateTimeParseException e) {
          // Continue to next formatter
        }
      }

      System.out.println("Could not parse date: " + dateStr);
      return null;

    } catch (Exception e) {
      System.out.println("Error parsing date '" + dateStr + "': " + e.getMessage());
      return null;
    }
  }

  private String getCodexIdFromDatabase(String modelName) {
    if (modelName == null || modelName.trim().isEmpty()) {
      System.out.println("Model name is empty, returning null for CODEX_ID");
      return null;
    }

    try {
      // Load SQL Server driver
      Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");

      try (Connection conn = DriverManager.getConnection(JDBC_URL, USERNAME, PASSWORD)) {
        String sql = "SELECT CODEX_ID FROM ALTAYYARLIVE.dbo.CAR_MODELS cm WHERE NAME = ?";
        PreparedStatement pstmt = conn.prepareStatement(sql);
        pstmt.setString(1, modelName.trim());

        System.out.println("Executing query for modelName: " + modelName);
        ResultSet resultSet = pstmt.executeQuery();

        if (resultSet.next()) {
          String codexId = resultSet.getString("CODEX_ID");
          System.out.println("Found CODEX_ID: " + codexId + " for modelName: " + modelName);
          return codexId != null ? codexId.trim() : null;
        } else {
          System.out.println("No CODEX_ID found for modelName: " + modelName);
          return null;
        }
      }
    } catch (Exception e) {
      System.out.println(
          "Database error while getting CODEX_ID for modelName '"
              + modelName
              + "': "
              + e.getMessage());
      e.printStackTrace();
      return null;
    }
  }

  private String getPurchasePriceFromDatabase(String assetId) {
    if (assetId == null || assetId.trim().isEmpty()) {
      System.out.println("Asset ID is empty, returning null for PURCHASE_PRICE");
      return null;
    }

    try {
      // Load SQL Server driver
      Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");

      try (Connection conn = DriverManager.getConnection(JDBC_URL, USERNAME, PASSWORD)) {
        String sql = "SELECT ct.PURCHASE_PRICE FROM ALTAYYARLIVE.dbo.CAR_TECHNICAL ct WHERE ct.UNIT_NO = ?";
        PreparedStatement pstmt = conn.prepareStatement(sql);
        pstmt.setString(1, assetId.trim());

        System.out.println("Executing query for assetId: " + assetId);
        ResultSet resultSet = pstmt.executeQuery();

        if (resultSet.next()) {
          String purchasePrice = resultSet.getString("PURCHASE_PRICE");
          System.out.println("Found PURCHASE_PRICE: " + purchasePrice + " for assetId: " + assetId);
          return purchasePrice != null ? purchasePrice.trim() : null;
        } else {
          System.out.println("No PURCHASE_PRICE found for assetId: " + assetId);
          return null;
        }
      }
    } catch (Exception e) {
      System.out.println(
          "Database error while getting PURCHASE_PRICE for assetId '"
              + assetId
              + "': "
              + e.getMessage());
      e.printStackTrace();
      return null;
    }
  }

  private boolean checkIfPlateExists(String plateNo) {
    if (plateNo == null || plateNo.trim().isEmpty()) {
      System.out.println("Plate number is empty, proceeding with vehicle creation");
      return false;
    }

    try {
      Thread.sleep(1000);
      RestTemplate restTemplate = new RestTemplate();
      String apiUrl = "https://api.lumirental.com/core-fleet-service/v3/vehicles?plateNo="
                      + java.net.URLEncoder.encode(plateNo.trim(), "UTF-8");

      System.out.println("Checking if plate exists: " + plateNo + " - API URL: " + apiUrl);

      // Set headers
      org.springframework.http.HttpHeaders headers = new org.springframework.http.HttpHeaders();
      org.springframework.http.HttpEntity<String> entity =
          new org.springframework.http.HttpEntity<>(headers);

      org.springframework.http.ResponseEntity<String> response =
          restTemplate.exchange(apiUrl, org.springframework.http.HttpMethod.GET, entity, String.class);

      System.out.println("API Response Status: " + response.getStatusCode());

      // If status is 200 (OK), vehicle exists
      if (response.getStatusCode().is2xxSuccessful()) {
//        System.out.println("Plate number " + plateNo + " already exists in system");
        return true;
      }

      return false;

    } catch (org.springframework.web.client.HttpClientErrorException e) {
      // If 404, vehicle doesn't exist - proceed
      if (e.getStatusCode().value() == 404) {
        System.out.println("Plate number " + plateNo + " not found (404) - proceeding with creation");
        return false;
      } else {
        System.out.println("API error checking plate " + plateNo + ": " + e.getStatusCode() + " - " + e.getMessage());
        // For other errors, assume vehicle doesn't exist to be safe
        return false;
      }
    } catch (Exception e) {
      System.out.println("Error checking if plate exists for " + plateNo + ": " + e.getMessage());
      e.printStackTrace();
      // If there's an error, assume vehicle doesn't exist to be safe
      return false;
    }
  }

  private void pushVehicleToKafka(Vehicle vehicle) {
    try {
      // Convert vehicle object to JSON string using CommonUtils
      String vehicleJson = CommonUtils.getStrFromObj(vehicle);

      if (vehicleJson == null || vehicleJson.trim().isEmpty()) {
        System.out.println("Failed to convert vehicle to JSON string");
        return;
      }

      RestTemplate restTemplate = new RestTemplate();
      String kafkaApiUrl = "https://api-dev.lumirental.com/core-fleet-service/v1/ops/kafka/push-message";

      System.out.println("Pushing vehicle to Kafka - API URL: " + kafkaApiUrl);
      System.out.println("Vehicle JSON: " + vehicleJson);

      // Create request body
      Map<String, Object> requestBody = new HashMap<>();
      requestBody.put("topic", "SyncVehicleFinancialData");
      requestBody.put("key", vehicle.getPlateNo()); // Use assetId as key

      // Parse vehicle JSON string back to object for the value field
      try {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.registerModule(new com.fasterxml.jackson.datatype.jsr310.JavaTimeModule());
        Object vehicleObject = objectMapper.readValue(vehicleJson, Object.class);
        requestBody.put("value", vehicleObject);
      } catch (Exception e) {
        System.out.println("Error parsing vehicle JSON, using string value: " + e.getMessage());
        requestBody.put("value", vehicleJson);
      }

      // Set headers
      org.springframework.http.HttpHeaders headers = new org.springframework.http.HttpHeaders();
      headers.setContentType(org.springframework.http.MediaType.APPLICATION_JSON);
      headers.setBearerAuth(
          "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJvUHRmcDZpd2ZpMmRBNzhVZVBuUV9KNHNfRjhhUXVucFBIRmVHa2xJQXFjIn0.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.QXE0WBPynNfqTtTmZlxGRgG4IPi2df2z1mmi-vflN8qk_eXIjsV7W7KgoJVMoFpMwuygI-ihPor5rvOcmf4RfbQeMUlEBvSZtNo4p4ziIVvGBISoTJyZEjjI-dpOAZBMOKOSD64LyyjRhc4eKoBlou726oru0DXxVkChqT8fwrsvagVCaJcI9iiLrbXqwpWnHyXEdFFM8XqSsp57rbLfPSUdjgTeDWcSG0XH8dhzstCDqSBnQ12VaTTpMOZxmQ3qLKraJjK2d9F6RjZ5Iy-ExEtbE7dxdJcQx04VNBwQPlu3qNPSHr1lGLOznNrlmIr53T3BEzqwkUArn5l-J109LA");

      org.springframework.http.HttpEntity<Map<String, Object>> entity =
          new org.springframework.http.HttpEntity<>(requestBody, headers);

      System.out.println("Request Body: " + CommonUtils.getStrFromObj(requestBody));

      org.springframework.http.ResponseEntity<String> response =
          restTemplate.exchange(kafkaApiUrl, org.springframework.http.HttpMethod.POST, entity, String.class);

      System.out.println("Kafka API Response Status: " + response.getStatusCode());
      System.out.println("Kafka API Response Body: " + response.getBody());

      if (response.getStatusCode().is2xxSuccessful()) {
        System.out.println("Successfully pushed vehicle data to Kafka topic: SyncVehicleFinancialData");
      } else {
        System.out.println("Failed to push vehicle data to Kafka. Status: " + response.getStatusCode());
      }

    } catch (Exception e) {
      System.out.println("Error pushing vehicle to Kafka: " + e.getMessage());
      e.printStackTrace();
    }
  }
}
