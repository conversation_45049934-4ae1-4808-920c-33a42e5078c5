package com.example.Driver.controller;

import com.example.Driver.model.Vehicle;
import org.springframework.web.bind.annotation.*;
import org.springframework.http.ResponseEntity;
import org.springframework.core.io.ClassPathResource;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.InputStream;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.HashMap;
import java.util.Map;
import java.util.List;
import java.util.ArrayList;
import java.sql.*;

@RestController
@RequestMapping("/api")
public class SyncVehicleController {

    // Database connection constants (same as SyncSpecification)
    static final String JDBC_URL =
        "**************************************************************************************************";
    static final String USERNAME = "lumi_digital";
    static final String PASSWORD = "Lu3!_D@2022";

  @GetMapping("/sync-vehicles")
  public ResponseEntity<Map<String, Object>> syncVehicles() {
    Map<String, Object> response = new HashMap<>();
    List<Vehicle> vehicles = new ArrayList<>();

    try {
      // Read Excel file from resources
      ClassPathResource resource = new ClassPathResource("All_Vehicle_Report.xlsx");
      InputStream inputStream = resource.getInputStream();

      Workbook workbook = new XSSFWorkbook(inputStream);
      Sheet sheet = workbook.getSheetAt(0); // Read first sheet

      System.out.println("Reading All_Vehicle_Report.xlsx...");
      System.out.println("Total rows: " + sheet.getLastRowNum());

      // Skip header row, start from row 1 (0-indexed)
      for (int rowIndex = 1; rowIndex <= 1; rowIndex++) {
        Row row = sheet.getRow(rowIndex);
        if (row != null) {
          try {
            // Extract data from specific columns
            String assetId = getCellValueAsString(row.getCell(0)); // Column 1: asset_id
            String plateNo = getCellValueAsString(row.getCell(1)); // Column 2: plate_no
            String make = getCellValueAsString(row.getCell(5)); // Column 6: make
            String modelName = getCellValueAsString(row.getCell(6)); // Column 7: model_name
            long modelYear = (long) row.getCell(7).getNumericCellValue(); // Column 8: model_year

            String color = getCellValueAsString(row.getCell(8)); // Column 9: color
            String purchaseDateStr =
                getCellValueAsString(row.getCell(20)); // Column 21: purchase_date
            String soldDateStr = getCellValueAsString(row.getCell(22)); // Column 22: sold_date

            // Parse dates
            LocalDate purchaseDate = parseDate(purchaseDateStr);
            LocalDate soldDate = parseDate(soldDateStr);

            // If sold_date is empty, set to '2001-01-01'
            if (soldDate == null || soldDateStr.trim().isEmpty()) {
              soldDate = LocalDate.of(2001, 1, 1);
            }

            // Get CODEX_ID from database using modelName
            String codexId = getCodexIdFromDatabase(modelName);

            // Create Vehicle object
            Vehicle vehicle =
                new Vehicle(assetId, plateNo, make, modelName, modelYear, color, purchaseDate, soldDate, codexId);
            vehicles.add(vehicle);

            // Print vehicle details
            System.out.println("Row " + (rowIndex + 1) + ": " + vehicle.toString());

          } catch (Exception e) {
            System.out.println("Error processing row " + (rowIndex + 1) + ": " + e.getMessage());
          }
        }
      }

      workbook.close();
      inputStream.close();

      // Print summary
      System.out.println("\n=== SYNC VEHICLES SUMMARY ===");
      System.out.println("Total vehicles processed: " + vehicles.size());
      System.out.println("\nAll Vehicles:");
      for (int i = 0; i < vehicles.size(); i++) {
        System.out.println((i + 1) + ". " + vehicles.get(i).toString());
      }

      response.put("success", true);
      response.put("totalVehicles", vehicles.size());
      response.put("vehicles", vehicles);
      response.put("message", "Vehicles synced successfully from All_Vehicle_Report.xlsx");

    } catch (Exception e) {
      response.put("success", false);
      response.put("error", "Error reading Excel file: " + e.getMessage());
      e.printStackTrace();
    }

    return ResponseEntity.ok(response);
  }

  private String getCellValueAsString(Cell cell) {
    if (cell == null) {
      return "";
    }

    switch (cell.getCellType()) {
      case STRING:
        return cell.getStringCellValue().trim();
      case NUMERIC:
        if (DateUtil.isValidExcelDate(cell.getNumericCellValue())) {
          // For date cells, format as date string
          return DateTimeFormatter.ofPattern("dd-MM-yyyy")
              .format(cell.getLocalDateTimeCellValue().toLocalDate());
        } else {
          // For numeric cells, return as string without decimal
          double numericValue = cell.getNumericCellValue();
          if (numericValue == (long) numericValue) {
            return String.valueOf((long) numericValue);
          } else {
            return String.valueOf(numericValue);
          }
        }
      case BOOLEAN:
        return String.valueOf(cell.getBooleanCellValue());
      case FORMULA:
        return cell.getCellFormula();
      default:
        return "";
    }
  }

  private LocalDate parseDate(String dateStr) {
    if (dateStr == null || dateStr.trim().isEmpty()) {
      return null;
    }

    try {
      // Try different date formats
      DateTimeFormatter[] formatters = {
        DateTimeFormatter.ofPattern("yyyy-MM-dd"),
        DateTimeFormatter.ofPattern("dd/MM/yyyy"),
        DateTimeFormatter.ofPattern("MM/dd/yyyy"),
        DateTimeFormatter.ofPattern("dd-MM-yyyy"),
        DateTimeFormatter.ofPattern("yyyy/MM/dd")
      };

      for (DateTimeFormatter formatter : formatters) {
        try {
          return LocalDate.parse(dateStr.trim(), formatter);
        } catch (DateTimeParseException e) {
          // Continue to next formatter
        }
      }

      System.out.println("Could not parse date: " + dateStr);
      return null;

    } catch (Exception e) {
      System.out.println("Error parsing date '" + dateStr + "': " + e.getMessage());
      return null;
    }
  }

  private String getCodexIdFromDatabase(String modelName) {
    if (modelName == null || modelName.trim().isEmpty()) {
      System.out.println("Model name is empty, returning null for CODEX_ID");
      return null;
    }

    try {
      // Load SQL Server driver
      Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");

      try (Connection conn = DriverManager.getConnection(JDBC_URL, USERNAME, PASSWORD)) {
        String sql = "SELECT CODEX_ID FROM ALTAYYARLIVE.dbo.CAR_MODELS cm WHERE NAME = ?";
        PreparedStatement pstmt = conn.prepareStatement(sql);
        pstmt.setString(1, modelName.trim());

        System.out.println("Executing query for modelName: " + modelName);
        ResultSet resultSet = pstmt.executeQuery();

        if (resultSet.next()) {
          String codexId = resultSet.getString("CODEX_ID");
          System.out.println("Found CODEX_ID: " + codexId + " for modelName: " + modelName);
          return codexId != null ? codexId.trim() : null;
        } else {
          System.out.println("No CODEX_ID found for modelName: " + modelName);
          return null;
        }
      }
    } catch (Exception e) {
      System.out.println(
          "Database error while getting CODEX_ID for modelName '"
              + modelName
              + "': "
              + e.getMessage());
      e.printStackTrace();
      return null;
    }
  }
}
