package com.example.Driver.controller;

import org.springframework.web.bind.annotation.*;
import org.springframework.http.ResponseEntity;
import java.util.HashMap;
import java.util.Map;
import java.util.List;
import java.util.ArrayList;
import java.sql.*;
import java.util.LinkedHashMap;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import java.io.InputStream;
import org.springframework.core.io.ClassPathResource;
import org.springframework.web.client.RestTemplate;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.JsonNode;

@RestController
@RequestMapping("/api")
public class SyncSpecification {

  // Database connection constants
  static final String JDBC_URL =
      "**************************************************************************************************";
  static final String USERNAME = "lumi_digital";
  static final String PASSWORD = "Lu3!_D@2022";

  @GetMapping("/sync-specs")
  public ResponseEntity<Map<String, Object>> syncSpecifications() {
    Map<String, Object> response = new HashMap<>();
    List<Map<String, Object>> allResults = new ArrayList<>();

    try {
      // Read Excel file from resources
      ClassPathResource resource = new ClassPathResource("Fleet_Vehicle_Specifications_2026.xlsx");
      InputStream inputStream = resource.getInputStream();

      Workbook workbook = new XSSFWorkbook(inputStream);

      // Read all sheets
      for (int sheetIndex = 0; sheetIndex < workbook.getNumberOfSheets(); sheetIndex++) {
        Sheet sheet = workbook.getSheetAt(sheetIndex);
        String sheetName = sheet.getSheetName();
        System.out.println("\n\nSheet: " + sheetName);

        String carproMake = null;
        String carproModel = null;
        Map<String, String> sheetDataMap = new LinkedHashMap<>();

        // Skip header row, start from row 1 (0-indexed)
        for (int rowIndex = 1; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
          Row row = sheet.getRow(rowIndex);
          if (row != null) {
            Cell keyCell = row.getCell(0); // First column (key)
            Cell valueCell = row.getCell(1); // Second column (value)

            if (keyCell != null && valueCell != null) {
              String key = getCellValueAsString(keyCell);
              String value = getCellValueAsString(valueCell);

              // Store data from row index 6 to 35 in map
              if (rowIndex >= 6 && rowIndex <= 35) {
                sheetDataMap.put(key, value);
                System.out.println(
                    "Sheet: "
                        + sheetName
                        + " - Row "
                        + (rowIndex + 1)
                        + ": "
                        + key
                        + " = "
                        + value);
              }

              // Check for carproMake (row 8, index 7) - "Model Carpro Make"
              if (rowIndex == 4 && "Model Carpro Make".equals(key)) {
                carproMake = value;
                System.out.println("Sheet: " + sheetName + " - Found carproMake: " + carproMake);
              }

              // Check for carproModel (row 9, index 8) - "Model Carpro Code"
              if (rowIndex == 5 && "Model Carpro Code".equals(key)) {
                carproModel = value;
                System.out.println("Sheet: " + sheetName + " - Found carproModel: " + carproModel);
              }
            }
          }
        }

        // Print the complete map for this sheet
        System.out.println("Sheet: " + sheetName + " - Complete Data Map (Rows 7-36):");
        for (Map.Entry<String, String> entry : sheetDataMap.entrySet()) {
          System.out.println("  " + entry.getKey() + " -> " + entry.getValue());
        }

        // If we found both values for this sheet, query the database
        if (carproMake != null
            && carproModel != null
            && !carproMake.isEmpty()
            && !carproModel.isEmpty()) {
          Map<String, Object> sheetResult = new HashMap<>();
          sheetResult.put("sheetName", sheetName);
          sheetResult.put("carproMake", carproMake);
          sheetResult.put("carproModel", carproModel);
          sheetResult.put("sheetDataMap", sheetDataMap);

          try {
            // Execute database query
            Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
            try (Connection conn = DriverManager.getConnection(JDBC_URL, USERNAME, PASSWORD)) {
              String sql =
                  "SELECT name, CODEX_ID FROM ALTAYYARLIVE.dbo.CAR_MODELS cm WHERE cm.CAR_MAKE = ? AND cm.MODEL = ?";
              PreparedStatement pstmt = conn.prepareStatement(sql);
              pstmt.setString(1, carproMake);
              pstmt.setString(2, carproModel);

              System.out.println(
                  "Sheet: "
                      + sheetName
                      + " - Executing query with carproMake="
                      + carproMake
                      + ", carproModel="
                      + carproModel);

              ResultSet resultSet = pstmt.executeQuery();

              if (resultSet.next()) {
                String name = resultSet.getString("name");
                String codexId = resultSet.getString("CODEX_ID");

                System.out.println(
                    "Sheet: "
                        + sheetName
                        + " - Query result - Name: "
                        + name.trim()
                        + ", CODEX_ID: "
                        + codexId.trim());

                sheetResult.put("success", true);
                // sheetResult.put("name", name != null ? name.trim() : null);
                sheetResult.put("codex_id", codexId != null ? codexId.trim() : null);

                // Call API to match materialId with codex_id
                try {
                  Map<String, Object> apiMatch =
                      findMatchingModel(codexId != null ? codexId.trim() : null);
                  if (apiMatch != null) {
                    Integer yaqueenModelId = (Integer) apiMatch.get("id");
                    sheetResult.put("yaqeen_model_id", yaqueenModelId);
                    sheetResult.put("yaqeen_model_name", apiMatch.get("name_en"));
                    sheetResult.put("capro_model_name", name != null ? name.trim() : null);
                    System.out.println(
                        "Sheet: "
                            + sheetName
                            + " - API Match found - Yaqeen Model ID: "
                            + yaqueenModelId
                            + ", Yaqeen Model Name: "
                            + apiMatch.get("name_en"));

                    // Call specification API and merge with sheetDataMap
                    try {
                      Map<String, Object> mergedSpecification =
                          getAndMergeSpecification(yaqueenModelId, sheetDataMap);
                      sheetResult.put("merged_specification", mergedSpecification);

                      // Update model specification
                      boolean updateSuccess =
                          updateModelSpecification(
                              yaqueenModelId, sheetDataMap, mergedSpecification);
                      sheetResult.put("specification_updated", updateSuccess);

                      System.out.println(
                          "Sheet: "
                              + sheetName
                              + " - Specification merged and updated successfully: "
                              + updateSuccess);
                    } catch (Exception specException) {
                      System.out.println(
                          "Sheet: "
                              + sheetName
                              + " - Specification API error: "
                              + specException.getMessage());
                      sheetResult.put(
                          "specification_error",
                          "Specification API failed: " + specException.getMessage());
                    }

                  } else {
                    System.out.println(
                        "Sheet: "
                            + sheetName
                            + " - No matching model found in API for codex_id: "
                            + codexId);
                    sheetResult.put("api_match_found", false);
                  }
                } catch (Exception apiException) {
                  System.out.println(
                      "Sheet: " + sheetName + " - API call error: " + apiException.getMessage());
                  sheetResult.put("api_error", "API call failed: " + apiException.getMessage());
                }
              } else {
                System.out.println(
                    "Sheet: " + sheetName + " - No matching record found in database");
                sheetResult.put("success", false);
                sheetResult.put("error", "No matching record found in database");
              }
            }
          } catch (Exception e) {
            System.out.println("Sheet: " + sheetName + " - Database error: " + e.getMessage());
            sheetResult.put("success", false);
            sheetResult.put("error", "Database error: " + e.getMessage());
          }

          allResults.add(sheetResult);
        } else {
          System.out.println(
              "Sheet: " + sheetName + " - Skipping (no valid carproMake/carproModel found)");
        }
      }

      workbook.close();
      inputStream.close();

      response.put("success", true);
      response.put("totalSheets", workbook.getNumberOfSheets());
      response.put("processedSheets", allResults.size());
      response.put("results", allResults);
      response.put("message", "All sheets processed successfully");

    } catch (Exception e) {
      response.put("success", false);
      response.put("error", "Error: " + e.getMessage());
      e.printStackTrace();
    }

    return ResponseEntity.ok(response);
  }

  private String getCellValueAsString(Cell cell) {
    switch (cell.getCellType()) {
      case STRING:
        return cell.getStringCellValue();
      case NUMERIC:
        if (DateUtil.isValidExcelDate(cell.getNumericCellValue())) {
          // For date cells, return just the integer value (day of month)
          return String.valueOf((int) cell.getNumericCellValue());
        } else {
          return String.valueOf((int) cell.getNumericCellValue());
        }
      case BOOLEAN:
        return String.valueOf(cell.getBooleanCellValue());
      case FORMULA:
        return cell.getCellFormula();
      default:
        return "";
    }
  }

  private Map<String, Object> findMatchingModel(String codexId) throws Exception {
    if (codexId == null || codexId.isEmpty()) {
      return null;
    }

    try {
      RestTemplate restTemplate = new RestTemplate();
      String apiUrl =
          "https://api.lumirental.com/core-fleet-service/v2/vehicle-model/all?pageNumber=0&pageSize=1000";

      System.out.println("Calling API: " + apiUrl);
      String response = restTemplate.getForObject(apiUrl, String.class);

      ObjectMapper mapper = new ObjectMapper();
      JsonNode rootNode = mapper.readTree(response);
      JsonNode contentArray = rootNode.get("content");

      if (contentArray != null && contentArray.isArray()) {
        for (JsonNode modelNode : contentArray) {
          JsonNode materialIdNode = modelNode.get("materialId");
          if (materialIdNode != null && !materialIdNode.isNull()) {
            String materialId = materialIdNode.asText();

            // Match materialId with codex_id
            if (codexId.equals(materialId)) {
              Map<String, Object> matchResult = new HashMap<>();
              matchResult.put("id", modelNode.get("id").asInt());
              matchResult.put("name_en", modelNode.get("name").get("en").asText());
              matchResult.put("material_id", materialId);

              System.out.println(
                  "Found matching model: ID="
                      + modelNode.get("id").asInt()
                      + ", Name="
                      + modelNode.get("name").get("en").asText()
                      + ", MaterialId="
                      + materialId);
              return matchResult;
            }
          }
        }
      }

      System.out.println("No matching model found for codex_id: " + codexId);
      return null;

    } catch (Exception e) {
      System.out.println("Error calling API: " + e.getMessage());
      e.printStackTrace();
      throw e;
    }
  }

  private Map<String, Object> getAndMergeSpecification(
      Integer yaqueenModelId, Map<String, String> sheetDataMap) throws Exception {
    try {
      RestTemplate restTemplate = new RestTemplate();
      String specApiUrl =
          "https://api-dev.lumirental.com/core-fleet-service/v2/vehicle-model/" + yaqueenModelId;

      System.out.println("Calling Specification API: " + specApiUrl);
      String response = restTemplate.getForObject(specApiUrl, String.class);

      ObjectMapper mapper = new ObjectMapper();
      JsonNode rootNode = mapper.readTree(response);
      JsonNode specificationNode = rootNode.get("specification");

      Map<String, Object> mergedSpecification = new HashMap<>();

      // Copy existing specification if it exists
      if (specificationNode != null) {
        mergedSpecification = mapper.convertValue(specificationNode, Map.class);
        System.out.println("Retrieved existing specification from API");
      }

      // Merge with sheetDataMap
      for (Map.Entry<String, String> entry : sheetDataMap.entrySet()) {
        String key = entry.getKey();
        String value = entry.getValue();
        mergedSpecification.put(key, value);
      }

      System.out.println("Merged specification with sheet data");
      return mergedSpecification;

    } catch (Exception e) {
      System.out.println("Error getting/merging specification: " + e.getMessage());
      e.printStackTrace();
      throw e;
    }
  }

  private boolean updateModelSpecification(
      Integer yaqueenModelId,
      Map<String, String> sheetDataMap,
      Map<String, Object> mergedSpecification)
      throws Exception {
    try {
      RestTemplate restTemplate = new RestTemplate();
      String updateApiUrl =
          "https://api-dev.lumirental.com/core-fleet-service/v2/vehicle-model/" + yaqueenModelId;

      // Create request body
      Map<String, Object> requestBody = new HashMap<>();
      requestBody.put("modelVersion", sheetDataMap.get("Model Version/Variant"));
      requestBody.put("modelYear", sheetDataMap.get("Model Year (manufacturing year)"));
      requestBody.put("specification", mergedSpecification);

      // Set headers
      org.springframework.http.HttpHeaders headers = new org.springframework.http.HttpHeaders();
      headers.setContentType(org.springframework.http.MediaType.APPLICATION_JSON);
      headers.setBearerAuth(
          "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJvUHRmcDZpd2ZpMmRBNzhVZVBuUV9KNHNfRjhhUXVucFBIRmVHa2xJQXFjIn0.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.QXE0WBPynNfqTtTmZlxGRgG4IPi2df2z1mmi-vflN8qk_eXIjsV7W7KgoJVMoFpMwuygI-ihPor5rvOcmf4RfbQeMUlEBvSZtNo4p4ziIVvGBISoTJyZEjjI-dpOAZBMOKOSD64LyyjRhc4eKoBlou726oru0DXxVkChqT8fwrsvagVCaJcI9iiLrbXqwpWnHyXEdFFM8XqSsp57rbLfPSUdjgTeDWcSG0XH8dhzstCDqSBnQ12VaTTpMOZxmQ3qLKraJjK2d9F6RjZ5Iy-ExEtbE7dxdJcQx04VNBwQPlu3qNPSHr1lGLOznNrlmIr53T3BEzqwkUArn5l-J109LA");

      org.springframework.http.HttpEntity<Map<String, Object>> entity =
          new org.springframework.http.HttpEntity<>(requestBody, headers);

      System.out.println("Calling Update API: " + updateApiUrl);
      System.out.println("Request Body: " + new ObjectMapper().writeValueAsString(requestBody));

      org.springframework.http.ResponseEntity<String> response =
          restTemplate.exchange(
              updateApiUrl, org.springframework.http.HttpMethod.PUT, entity, String.class);

      System.out.println("Update API Response Status: " + response.getStatusCode());
      System.out.println("Update API Response Body: " + response.getBody());

      return response.getStatusCode().is2xxSuccessful();

    } catch (Exception e) {
      System.out.println("Error updating model specification: " + e.getMessage());
      e.printStackTrace();
      throw e;
    }
  }
}
