package com.example.Driver;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import java.io.IOException;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class CommonUtils {

  private static final Logger log = LoggerFactory.getLogger("application");

  private static final ObjectMapper mapper =
      JsonMapper.builder()
          .configure(MapperFeature.ACCEPT_CASE_INSENSITIVE_PROPERTIES, true)
          .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
          .addModule(new JavaTimeModule())
          .build();

  public static String getStrFromObj(Object obj) {
    String data = null;
    try {
      data = mapper.writeValueAsString(obj);
    } catch (JsonProcessingException e) {
      // Swallow Exception
      log.error("Error while converting object to string", e);
    }
    return data;
  }

  public static <T> T getObjFromStr(String strObj, Class<T> resClass) throws IOException {
    return mapper.readValue(strObj, resClass);
  }
}
