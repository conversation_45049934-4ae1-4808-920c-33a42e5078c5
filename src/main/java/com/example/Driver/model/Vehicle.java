package com.example.Driver.model;

import java.time.LocalDate;

public class Vehicle {
    private String assetId;
    private String plateNo;
    private String modelName;
    private String modelYear;
    private String color;
    private LocalDate purchaseDate;
    private LocalDate soldDate;

    // Default constructor
    public Vehicle() {}

    // Constructor with all fields
    public Vehicle(String assetId, String plateNo, String modelName, String modelYear, 
                   String color, LocalDate purchaseDate, LocalDate soldDate) {
        this.assetId = assetId;
        this.plateNo = plateNo;
        this.modelName = modelName;
        this.modelYear = modelYear;
        this.color = color;
        this.purchaseDate = purchaseDate;
        this.soldDate = soldDate;
    }

    // Getters and Setters
    public String getAssetId() {
        return assetId;
    }

    public void setAssetId(String assetId) {
        this.assetId = assetId;
    }

    public String getPlateNo() {
        return plateNo;
    }

    public void setPlateNo(String plateNo) {
        this.plateNo = plateNo;
    }

    public String getModelName() {
        return modelName;
    }

    public void setModelName(String modelName) {
        this.modelName = modelName;
    }

    public String getModelYear() {
        return modelYear;
    }

    public void setModelYear(String modelYear) {
        this.modelYear = modelYear;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }

    public LocalDate getPurchaseDate() {
        return purchaseDate;
    }

    public void setPurchaseDate(LocalDate purchaseDate) {
        this.purchaseDate = purchaseDate;
    }

    public LocalDate getSoldDate() {
        return soldDate;
    }

    public void setSoldDate(LocalDate soldDate) {
        this.soldDate = soldDate;
    }

    @Override
    public String toString() {
        return "Vehicle{" +
                "assetId='" + assetId + '\'' +
                ", plateNo='" + plateNo + '\'' +
                ", modelName='" + modelName + '\'' +
                ", modelYear='" + modelYear + '\'' +
                ", color='" + color + '\'' +
                ", purchaseDate=" + purchaseDate +
                ", soldDate=" + soldDate +
                '}';
    }
}
