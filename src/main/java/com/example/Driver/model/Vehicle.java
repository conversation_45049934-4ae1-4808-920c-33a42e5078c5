package com.example.Driver.model;

import jdk.jfr.DataAmount;
import lombok.Data;

import java.time.LocalDate;

@Data
public class Vehicle {
    private String assetId;
    private String plateNo;
    private String materialId;
    private String make;
    private String model;
    private long modelYear;
    private String color;
    private Double nbv;
    private Double adv;
    private LocalDate purchaseDate;
    private Double purchasePrice;
    private LocalDate deactivationOn;


    // Default constructor
    public Vehicle() {}

    // Constructor with all fields
    public Vehicle(String assetId, String plateNo, String make ,String modelName, long modelYear,
                   String color, String purchasePrice, LocalDate purchaseDate, LocalDate soldDate, String codexId) {
        this.assetId = assetId;
        this.plateNo = plateNo;
        this.materialId = codexId;
        this.make = make;
        this.model = modelName;
        this.modelYear = modelYear;
        this.color = color;
        this.nbv = 0.0;
        this.adv = Double.parseDouble(purchasePrice)-(Double.parseDouble(purchasePrice)/10);
        this.purchaseDate = purchaseDate;
        this.purchasePrice = Double.parseDouble(purchasePrice);
        this.deactivationOn = soldDate;
    }


}
