package com.example.Driver.model;

import jdk.jfr.DataAmount;
import lombok.Data;

import java.time.LocalDate;

@Data
public class Vehicle {
    private String assetId;
    private String plateNo;
    private String materialId;
    private String make;
    private String model;
    private String modelYear;
    private String color;
    private String nbv;
    private String adv;
    private LocalDate purchaseDate;
    private String purchasePrice;
    private LocalDate deactivationOn;


    // Default constructor
    public Vehicle() {}

    // Constructor with all fields
    public Vehicle(String assetId, String plateNo, String make ,String modelName, String modelYear,
                   String color, LocalDate purchaseDate, LocalDate soldDate, String codexId) {
        this.assetId = assetId;
        this.plateNo = plateNo;
        this.materialId = codexId;
        this.make = make;
        this.model = modelName;
        this.modelYear = modelYear;
        this.color = color;
        this.nbv = "";
        this.adv = "";
        this.purchaseDate = purchaseDate;
        this.purchasePrice = "";
        this.deactivationOn = soldDate;
    }


}
